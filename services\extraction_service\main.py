"""
Information Extraction Microservice
Handles XML parsing and CUFE value extraction
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.responses import JSONResponse
import uvicorn
import os
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from lxml import etree
import re

from shared.schemas.extraction import (
    ExtractionRequest,
    ExtractionResponse,
    CUFEData,
    BatchExtractionRequest,
    BatchExtractionResponse,
    BatchExtractionResult
)
from shared.database.connection import get_db
from shared.utils.logger import get_logger
from shared.database.models import CUFERecord, ExtractedFileRecord

# Initialize FastAPI app
app = FastAPI(
    title="Extraction Service",
    description="Microservice for extracting CUFE values from XML files",
    version="1.0.0"
)

logger = get_logger(__name__)

# UBL namespaces for Colombian electronic invoices
UBL_NAMESPACES = {
    'cbc': 'urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2',
    'cac': 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2',
    'ext': 'urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2',
    'sts': 'dian:gov:co:facturaelectronica:Structures-2-1',
    'invoice': 'urn:oasis:names:specification:ubl:schema:xsd:Invoice-2'
}

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "extraction-service"}

# Extraction endpoints
@app.post("/extract-cufe", response_model=ExtractionResponse)
async def extract_cufe(
    request: ExtractionRequest,
    db: Session = Depends(get_db)
):
    """
    Extract CUFE value from XML file
    """
    try:
        logger.info(f"Extracting CUFE from XML file: {request.xml_file_path}")

        # Validate XML file exists
        if not os.path.exists(request.xml_file_path):
            raise HTTPException(status_code=404, detail=f"XML file not found: {request.xml_file_path}")

        # Parse XML and extract CUFE
        cufe_data = _extract_cufe_from_xml(request.xml_file_path, request.extract_additional_data)

        if not cufe_data.cufe_value:
            logger.warning(f"No CUFE found in XML file: {request.xml_file_path}")
            return ExtractionResponse(
                success=False,
                cufe_value="",
                xml_file_path=request.xml_file_path,
                message="No CUFE found in XML file",
                errors=["CUFE element not found in XML structure"]
            )

        # Store CUFE record in database if email_id is provided
        if request.email_id:
            try:
                _store_cufe_record(cufe_data, request.xml_file_path, request.email_id, db)
                db.commit()
                logger.info(f"CUFE record stored in database: {cufe_data.cufe_value}")
            except Exception as e:
                logger.error(f"Failed to store CUFE record: {str(e)}")
                db.rollback()
                # Continue with response even if database storage fails

        return ExtractionResponse(
            success=True,
            cufe_value=cufe_data.cufe_value,
            xml_file_path=request.xml_file_path,
            message="CUFE extraction completed successfully",
            cufe_data=cufe_data,
            extraction_date=datetime.now()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting CUFE: {str(e)}")
        raise HTTPException(status_code=500, detail=f"CUFE extraction failed: {str(e)}")

def _extract_cufe_from_xml(xml_file_path: str, extract_additional_data: bool = True) -> CUFEData:
    """
    Extract CUFE and additional data from Colombian UBL XML invoice
    """
    try:
        # Parse XML file
        with open(xml_file_path, 'rb') as xml_file:
            tree = etree.parse(xml_file)
            root = tree.getroot()

        # Extract CUFE from cbc:UUID element
        cufe_value = ""
        uuid_elements = root.xpath('//cbc:UUID[@schemeName="CUFE"]', namespaces=UBL_NAMESPACES)

        if uuid_elements:
            cufe_value = uuid_elements[0].text.strip() if uuid_elements[0].text else ""
        else:
            # Try alternative xpath patterns
            uuid_elements = root.xpath('//cbc:UUID', namespaces=UBL_NAMESPACES)
            for uuid_elem in uuid_elements:
                scheme_name = uuid_elem.get('schemeName', '')
                if scheme_name.upper() == 'CUFE':
                    cufe_value = uuid_elem.text.strip() if uuid_elem.text else ""
                    break

        additional_fields = {}
        issuer_name = None
        document_number = None
        issue_date = None
        total_amount = None

        if extract_additional_data and cufe_value:
            # Extract issuer name
            issuer_elements = root.xpath('//cac:AccountingSupplierParty/cac:Party/cac:PartyName/cbc:Name', namespaces=UBL_NAMESPACES)
            if issuer_elements:
                issuer_name = issuer_elements[0].text.strip() if issuer_elements[0].text else None

            # Extract document number
            id_elements = root.xpath('//cbc:ID', namespaces=UBL_NAMESPACES)
            if id_elements:
                document_number = id_elements[0].text.strip() if id_elements[0].text else None

            # Extract issue date
            issue_date_elements = root.xpath('//cbc:IssueDate', namespaces=UBL_NAMESPACES)
            if issue_date_elements:
                try:
                    date_str = issue_date_elements[0].text.strip()
                    issue_date = datetime.strptime(date_str, '%Y-%m-%d')
                except (ValueError, AttributeError):
                    logger.warning(f"Could not parse issue date: {issue_date_elements[0].text}")

            # Extract total amount
            total_elements = root.xpath('//cac:LegalMonetaryTotal/cbc:TaxInclusiveAmount', namespaces=UBL_NAMESPACES)
            if total_elements:
                total_amount = total_elements[0].text.strip() if total_elements[0].text else None

            # Extract additional fields for debugging
            additional_fields = {
                'xml_root_tag': root.tag,
                'namespaces_found': list(root.nsmap.keys()) if hasattr(root, 'nsmap') else [],
                'uuid_elements_count': len(uuid_elements)
            }

        return CUFEData(
            cufe_value=cufe_value,
            issuer_name=issuer_name,
            document_number=document_number,
            issue_date=issue_date,
            total_amount=total_amount,
            additional_fields=additional_fields
        )

    except etree.XMLSyntaxError as e:
        logger.error(f"XML parsing error for {xml_file_path}: {str(e)}")
        raise ValueError(f"Invalid XML file: {str(e)}")
    except Exception as e:
        logger.error(f"Error extracting CUFE from {xml_file_path}: {str(e)}")
        raise

def _store_cufe_record(cufe_data: CUFEData, xml_file_path: str, email_id: str, db: Session):
    """
    Store CUFE record in database
    """
    # Find the XML file record
    xml_file_record = db.query(ExtractedFileRecord).filter(
        ExtractedFileRecord.file_path == xml_file_path,
        ExtractedFileRecord.file_type == "xml"
    ).first()

    if not xml_file_record:
        logger.warning(f"XML file record not found for {xml_file_path}")
        return

    # Check if CUFE already exists
    existing_cufe = db.query(CUFERecord).filter(
        CUFERecord.cufe_value == cufe_data.cufe_value
    ).first()

    if existing_cufe:
        logger.info(f"CUFE already exists in database: {cufe_data.cufe_value}")
        return existing_cufe

    # Create new CUFE record
    cufe_record = CUFERecord(
        cufe_value=cufe_data.cufe_value,
        email_record_id=xml_file_record.zip_file_record.email_record.id if xml_file_record.zip_file_record and xml_file_record.zip_file_record.email_record else None,
        xml_file_record_id=xml_file_record.id,
        issuer_name=cufe_data.issuer_name,
        document_number=cufe_data.document_number,
        issue_date=cufe_data.issue_date,
        total_amount=cufe_data.total_amount,
        extraction_date=datetime.now()
    )

    db.add(cufe_record)
    return cufe_record

@app.post("/batch-extract", response_model=BatchExtractionResponse)
async def batch_extract_cufe(
    request: BatchExtractionRequest,
    db: Session = Depends(get_db)
):
    """
    Extract CUFE values from multiple XML files
    """
    try:
        logger.info(f"Batch extracting CUFE from {len(request.xml_file_paths)} XML files")

        results = []
        successful_extractions = 0
        failed_extractions = 0

        for i, xml_file_path in enumerate(request.xml_file_paths):
            try:
                # Extract CUFE from XML file
                cufe_data = _extract_cufe_from_xml(xml_file_path, True)

                if cufe_data.cufe_value:
                    # Store in database if email_id is provided
                    email_id = request.email_ids[i] if request.email_ids and i < len(request.email_ids) else None
                    if email_id:
                        try:
                            _store_cufe_record(cufe_data, xml_file_path, email_id, db)
                        except Exception as e:
                            logger.warning(f"Failed to store CUFE record for {xml_file_path}: {str(e)}")

                    results.append(BatchExtractionResult(
                        xml_file_path=xml_file_path,
                        success=True,
                        cufe_value=cufe_data.cufe_value,
                        cufe_data=cufe_data
                    ))
                    successful_extractions += 1
                else:
                    results.append(BatchExtractionResult(
                        xml_file_path=xml_file_path,
                        success=False,
                        error_message="No CUFE found in XML file"
                    ))
                    failed_extractions += 1

            except Exception as e:
                logger.error(f"Error processing {xml_file_path}: {str(e)}")
                results.append(BatchExtractionResult(
                    xml_file_path=xml_file_path,
                    success=False,
                    error_message=str(e)
                ))
                failed_extractions += 1

        # Commit database changes
        try:
            db.commit()
        except Exception as e:
            logger.error(f"Database commit failed: {str(e)}")
            db.rollback()

        return BatchExtractionResponse(
            success=failed_extractions == 0,
            message=f"Batch CUFE extraction completed. {successful_extractions} successful, {failed_extractions} failed.",
            processed_count=len(request.xml_file_paths),
            successful_extractions=successful_extractions,
            failed_extractions=failed_extractions,
            results=results
        )

    except Exception as e:
        logger.error(f"Error in batch CUFE extraction: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Batch extraction failed: {str(e)}")

@app.get("/cufe/{cufe_value}")
async def get_cufe_info(cufe_value: str, db: Session = Depends(get_db)):
    """Get CUFE information by CUFE value"""
    try:
        cufe_record = db.query(CUFERecord).filter(
            CUFERecord.cufe_value == cufe_value
        ).first()

        if not cufe_record:
            raise HTTPException(status_code=404, detail=f"CUFE not found: {cufe_value}")

        return {
            "cufe_value": cufe_record.cufe_value,
            "issuer_name": cufe_record.issuer_name,
            "document_number": cufe_record.document_number,
            "issue_date": cufe_record.issue_date,
            "total_amount": cufe_record.total_amount,
            "extraction_date": cufe_record.extraction_date,
            "xml_file_path": cufe_record.xml_file_record.file_path if cufe_record.xml_file_record else None
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving CUFE info: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve CUFE info: {str(e)}")

@app.get("/status")
async def get_extraction_status():
    """Get current extraction status"""
    return {
        "service": "extraction-service",
        "status": "running",
        "supported_formats": ["UBL 2.1 XML"],
        "supported_namespaces": list(UBL_NAMESPACES.keys()),
        "cufe_xpath": "//cbc:UUID[@schemeName='CUFE']"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8003)),
        reload=os.getenv("DEBUG", "false").lower() == "true"
    )
